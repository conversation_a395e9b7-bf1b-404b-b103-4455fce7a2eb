# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

Your Next Store (YNS) is a modern e-commerce platform built with Next.js 15 (App Router), TypeScript, and Stripe integration. It's designed as a complete e-commerce solution with server-side rendering, internationalization, and AI-powered features.

## Development Commands

### Package Management & Development
```bash
# Install dependencies
bun install

# Development server (with Turbo)
bun dev               # Starts on localhost:3000

# Build & deployment
bun run build         # Production build
bun run start         # Start production server

# Testing
bun run test          # Run Vitest tests
vitest                # Direct Vitest execution

# Code Quality
bun run lint          # Run Biome linter and formatter
biome check --write   # Direct Biome execution

# Docker
bun run docker:build
bun run docker:run
```

### Environment Setup
Copy `.env.example` to `.env` and configure required Stripe keys:
- `NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY`
- `STRIPE_SECRET_KEY` 
- `STRIPE_CURRENCY`

## Architecture Overview

### Core Structure
- **Next.js 15 App Router** with React 19 and experimental features (PPR, React Compiler)
- **Stripe-centric approach**: Products, pricing, and metadata managed entirely in Stripe
- **Commerce Kit**: Custom commerce library (`commerce-kit` package) for Stripe operations
- **Server Components first** with selective client components
- **TypeScript strict mode** with comprehensive type safety

### Key Architectural Patterns

#### Stripe Integration
- Products defined in Stripe with metadata fields (`slug`, `category`, `variant`, `order`)
- Cart state managed via secure HTTP-only cookies (`yns_cart`)
- Webhook handling for payment events at `/api/stripe-webhook`
- Tax calculation via Stripe Tax (optional)

#### Authentication & Authorization
- JWT-based sessions using `jose` library (`src/lib/auth.ts:23`)
- Middleware protection for admin routes (`src/middleware.ts:5`)
- Simple email/password auth for order management

#### Internationalization
- Next-intl integration with message files in `/messages/`
- Server-side translation functions in `src/i18n/server.tsx`
- Client-side provider in `src/i18n/client.tsx`

#### State Management
- React Context for cart modal state (`src/context/cart-modal.tsx`)
- Server Actions for cart operations (`src/actions/cart-actions.ts`)
- No global state management library - relies on React 19 features

### Directory Structure
```
src/
├── app/                    # App Router pages and API routes
│   ├── (store)/           # Store-facing pages (grouped route)
│   └── api/               # API endpoints (Stripe webhook, chat)
├── actions/               # Server Actions
├── lib/                   # Utilities and business logic
│   ├── search/           # Search implementations (simple, Trieve)
│   ├── auth.ts           # JWT session management
│   └── cart.ts           # Cart cookie utilities
├── ui/                    # UI components
│   ├── shadcn/           # shadcn/ui components
│   ├── checkout/         # Checkout flow components
│   └── nav/              # Navigation components
└── i18n/                 # Internationalization
```

## Development Guidelines

### TypeScript Configuration
- **Strict mode enabled** with `noUncheckedIndexedAccess`
- Path aliases: `@/*` → `src/*`, `@ui/*` → `src/ui/*`
- ES2022 target with modern module resolution

### Code Quality Tools
- **Biome** for linting and formatting (replaces ESLint + Prettier)
- Tab indentation, 110 character line width
- Strict rules: no `any`, use `as const`, import type enforcement

### Testing Setup
- **Vitest** with React Testing Library
- Test files: `**/?(*.)test.?(c|m)[jt]s?(x)`
- Global test setup in `src/setup-tests.ts`
- Mock reset and restoration enabled

### Stripe Product Configuration
Products require specific metadata in Stripe dashboard:
- `slug` (required): URL identifier, shared across variants
- `category` (optional): Product grouping
- `variant` (optional): Size, color, etc. variations
- `order` (optional): Display ordering (lower = first)

### Search Implementation
Multiple search backends supported:
- Simple search (`src/lib/search/simplesearch.ts`)
- Trieve integration (`src/lib/search/trieve.ts`) 
- Configurable via environment variables

### Enhanced Search Features
- Advanced fuzzy search with Fuse.js (`src/lib/search/enhanced-search.ts`)
- Smart product recommendations based on categories and popularity
- Enhanced search UI with filters and autocomplete (`src/ui/enhanced-search.tsx`)
- Product recommendations component (`src/ui/product-recommendations.tsx`)

## Common Development Patterns

### Server Components & Actions
Most components are Server Components. Client components marked with `"use client"` and typically handle:
- Interactive forms and modals
- Real-time state updates
- Browser-only features

### Commerce Kit Usage
Import commerce functions from the `commerce-kit` package:
```typescript
import { productGet, cartCreate } from "commerce-kit"
```

### Error Handling
- Global error boundaries in `app/error.tsx` and `app/global-error.tsx`
- Stripe webhook error handling with proper response codes
- Form validation using Zod schemas

### Performance Optimizations
- Image optimization with Vercel/Stripe CDN support
- Experimental features: PPR, React Compiler, MDX-RS
- Bundle optimization via webpack configuration

## Deployment Notes

### Environment Variables
Required for production:
- Stripe keys and currency configuration
- `NEXT_PUBLIC_URL` for absolute URLs
- Optional: Umami analytics, newsletter endpoint, tax settings

### Vercel Deployment
- Requires `ENABLE_EXPERIMENTAL_COREPACK=1` 
- Automatic URL detection via Vercel environment variables
- Edge Config support for feature flags

### Docker Support
- Standalone output mode when `DOCKER=1`
- Multi-stage build optimization
- Production-ready container configuration