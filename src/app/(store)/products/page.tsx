import * as Commerce from "commerce-kit";
import type { Metadata } from "next/types";
import { publicUrl } from "@/env.mjs";
import { getTranslations } from "@/i18n/server";
import { ProductList } from "@/ui/products/product-list";

export const generateMetadata = async (): Promise<Metadata> => {
	const t = await getTranslations("/products.metadata");
	return {
		title: t("title"),
		alternates: { canonical: `${publicUrl}/products` },
	};
};

export default async function AllProductsPage() {
	let products: Awaited<ReturnType<typeof Commerce.productBrowse>> = [];

	try {
		products = await Commerce.productBrowse({ first: 100 });
	} catch (error) {
		console.error("Failed to fetch products for products page:", error);
		products = [];
	}

	const t = await getTranslations("/products.page");

	return (
		<main className="pb-8">
			<h1 className="text-3xl font-bold leading-none tracking-tight text-foreground">{t("title")}</h1>
			<ProductList products={products} />
		</main>
	);
}
