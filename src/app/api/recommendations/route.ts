import { type NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { getPopularProducts, getRecommendations } from "@/lib/search/enhanced-search";

// Request validation schema
const recommendationsRequestSchema = z.object({
	productId: z.string().optional(),
	category: z.string().optional(),
	limit: z.number().min(1).max(20).default(4),
});

export async function GET(request: NextRequest) {
	try {
		const { searchParams } = new URL(request.url);

		const productId = searchParams.get("productId") || undefined;
		const category = searchParams.get("category") || undefined;
		const limit = parseInt(searchParams.get("limit") || "4");

		// Validate parameters
		const validatedData = recommendationsRequestSchema.parse({
			productId,
			category,
			limit,
		});

		let products;

		if (validatedData.productId) {
			// Get recommendations based on specific product
			products = await getRecommendations(validatedData.productId, validatedData.limit);
		} else if (validatedData.category) {
			// Get popular products in specific category
			products = await getPopularProducts(validatedData.category, validatedData.limit);
		} else {
			// Get general popular products
			products = await getPopularProducts(undefined, validatedData.limit);
		}

		return NextResponse.json(
			{
				products,
				count: products.length,
			},
			{
				status: 200,
				headers: {
					"Cache-Control": "public, s-maxage=600, stale-while-revalidate=1200",
				},
			},
		);
	} catch (error) {
		console.error("Recommendations API error:", error);

		if (error instanceof z.ZodError) {
			return NextResponse.json(
				{
					error: "Invalid request parameters",
					details: error.errors,
				},
				{ status: 400 },
			);
		}

		return NextResponse.json(
			{
				error: "Internal server error",
				message: "Failed to fetch recommendations",
			},
			{ status: 500 },
		);
	}
}
