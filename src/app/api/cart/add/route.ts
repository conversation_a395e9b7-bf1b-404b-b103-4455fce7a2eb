import { type NextRequest, NextResponse } from "next/server";
import { addToCartAction } from "@/actions/cart-actions";

export async function POST(request: NextRequest) {
	try {
		const formData = await request.formData();
		const productId = formData.get("productId");

		if (!productId || typeof productId !== "string") {
			return NextResponse.json({ error: "Product ID is required" }, { status: 400 });
		}

		const cart = await addToCartAction(formData);

		if (cart) {
			return NextResponse.json(
				{
					success: true,
					cartId: cart.id,
					message: "Product added to cart successfully",
				},
				{ status: 200 },
			);
		} else {
			return NextResponse.json({ error: "Failed to add product to cart" }, { status: 500 });
		}
	} catch (error) {
		console.error("Add to cart API error:", error);
		return NextResponse.json({ error: "Internal server error" }, { status: 500 });
	}
}
