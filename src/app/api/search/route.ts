import { type NextRequest, NextResponse } from "next/server";
import { z } from "zod";
import { enhancedSearch, type SearchOptions } from "@/lib/search/enhanced-search";

// Request validation schema
const searchRequestSchema = z.object({
	query: z.string().default(""),
	filters: z
		.object({
			category: z.string().optional(),
			priceRange: z
				.object({
					min: z.number().min(0),
					max: z.number().min(0),
				})
				.optional(),
			sortBy: z.enum(["relevance", "price-asc", "price-desc", "name", "newest"]).optional(),
		})
		.optional(),
	limit: z.number().min(1).max(100).default(20),
	offset: z.number().min(0).default(0),
});

export async function POST(request: NextRequest) {
	try {
		const body = await request.json();

		// Validate request body
		const validatedData = searchRequestSchema.parse(body);

		// Perform enhanced search
		const searchOptions: SearchOptions = {
			query: validatedData.query,
			filters: validatedData.filters,
			limit: validatedData.limit,
			offset: validatedData.offset,
		};

		const searchResult = await enhancedSearch(searchOptions);

		return NextResponse.json(searchResult, {
			status: 200,
			headers: {
				"Cache-Control": "public, s-maxage=300, stale-while-revalidate=600",
			},
		});
	} catch (error) {
		console.error("Search API error:", error);

		if (error instanceof z.ZodError) {
			return NextResponse.json(
				{
					error: "Invalid request parameters",
					details: error.errors,
				},
				{ status: 400 },
			);
		}

		return NextResponse.json(
			{
				error: "Internal server error",
				message: "Failed to perform search",
			},
			{ status: 500 },
		);
	}
}

// GET endpoint for simple searches via URL parameters
export async function GET(request: NextRequest) {
	try {
		const { searchParams } = new URL(request.url);

		const query = searchParams.get("q") || "";
		const category = searchParams.get("category") || undefined;
		const sortBy =
			(searchParams.get("sort") as "relevance" | "price-asc" | "price-desc" | "name" | "newest") || undefined;
		const limit = parseInt(searchParams.get("limit") || "20");
		const offset = parseInt(searchParams.get("offset") || "0");

		// Validate parameters
		const validatedData = searchRequestSchema.parse({
			query,
			filters: {
				category,
				sortBy,
			},
			limit,
			offset,
		});

		// Perform enhanced search
		const searchOptions: SearchOptions = {
			query: validatedData.query,
			filters: validatedData.filters,
			limit: validatedData.limit,
			offset: validatedData.offset,
		};

		const searchResult = await enhancedSearch(searchOptions);

		return NextResponse.json(searchResult, {
			status: 200,
			headers: {
				"Cache-Control": "public, s-maxage=300, stale-while-revalidate=600",
			},
		});
	} catch (error) {
		console.error("Search API error:", error);

		if (error instanceof z.ZodError) {
			return NextResponse.json(
				{
					error: "Invalid request parameters",
					details: error.errors,
				},
				{ status: 400 },
			);
		}

		return NextResponse.json(
			{
				error: "Internal server error",
				message: "Failed to perform search",
			},
			{ status: 500 },
		);
	}
}
