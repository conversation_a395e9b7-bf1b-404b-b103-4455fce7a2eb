import * as Commerce from "commerce-kit";
import { z } from "zod";

/**
 * Safe wrapper for Commerce.productBrowse that handles products with missing slug metadata
 * This prevents Zod validation errors when Stripe products don't have required metadata
 */
export async function safeProductBrowse(
	options: Parameters<typeof Commerce.productBrowse>[0] = {}
): Promise<Commerce.MappedProduct[]> {
	try {
		return await Commerce.productBrowse(options);
	} catch (error) {
		// Handle Zod validation errors for missing slug metadata
		if (error instanceof z.ZodError) {
			const slugErrors = error.errors.filter(
				(err) => err.path.includes("slug") && err.code === "invalid_type"
			);
			
			if (slugErrors.length > 0) {
				console.warn(
					"Some products are missing required slug metadata. Please configure slug metadata in your Stripe products:",
					slugErrors
				);
				
				// Return empty array to prevent app crashes
				// Products will be properly displayed once slug metadata is configured
				return [];
			}
		}
		
		// Re-throw non-slug related errors
		throw error;
	}
}

/**
 * Safe wrapper for Commerce.productGet that handles products with missing slug metadata
 */
export async function safeProductGet(
	options: Parameters<typeof Commerce.productGet>[0]
): Promise<Commerce.MappedProduct | null> {
	try {
		return await Commerce.productGet(options);
	} catch (error) {
		// Handle Zod validation errors for missing slug metadata
		if (error instanceof z.ZodError) {
			const slugErrors = error.errors.filter(
				(err) => err.path.includes("slug") && err.code === "invalid_type"
			);
			
			if (slugErrors.length > 0) {
				console.warn(
					"Product is missing required slug metadata. Please configure slug metadata in your Stripe product:",
					slugErrors
				);
				
				// Return null to indicate product not found/invalid
				return null;
			}
		}
		
		// Re-throw non-slug related errors
		throw error;
	}
}