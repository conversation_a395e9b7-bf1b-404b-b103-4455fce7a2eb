import * as Commerce from "commerce-kit";
import { safeProductBrowse } from "@/lib/safe-commerce";
import Fuse, { type IFuseOptions } from "fuse.js";
import { unstable_cache } from "next/cache";

export interface SearchFilters {
	category?: string;
	priceRange?: {
		min: number;
		max: number;
	};
	sortBy?: "relevance" | "price-asc" | "price-desc" | "name" | "newest";
}

export interface SearchResult {
	products: Commerce.MappedProduct[];
	totalCount: number;
	suggestions: string[];
	categories: string[];
	priceRange: {
		min: number;
		max: number;
	};
}

export interface SearchOptions {
	query: string;
	filters?: SearchFilters;
	limit?: number;
	offset?: number;
}

// Fuse.js configuration for fuzzy search
const fuseOptions: IFuseOptions<Commerce.MappedProduct> = {
	keys: [
		{ name: "name", weight: 0.7 },
		{ name: "description", weight: 0.3 },
		{ name: "metadata.category", weight: 0.5 },
		{ name: "metadata.variant", weight: 0.2 },
	],
	threshold: 0.4, // Lower = more strict matching
	distance: 100,
	minMatchCharLength: 2,
	includeScore: true,
	includeMatches: true,
};

// Cache for search index
let searchIndex: Fuse<Commerce.MappedProduct> | null = null;
let cachedProducts: Commerce.MappedProduct[] = [];

async function getSearchIndex(): Promise<Fuse<Commerce.MappedProduct>> {
	if (searchIndex && cachedProducts.length > 0) {
		return searchIndex;
	}

	try {
		const products = await safeProductBrowse({ first: 1000 });
		cachedProducts = products;
		searchIndex = new Fuse(products, fuseOptions);
		return searchIndex;
	} catch (error) {
		console.error("Failed to build search index:", error);
		throw new Error("Search index unavailable");
	}
}

function applyFilters(products: Commerce.MappedProduct[], filters?: SearchFilters): Commerce.MappedProduct[] {
	if (!filters) return products;

	let filtered = products;

	// Category filter
	if (filters.category) {
		filtered = filtered.filter(
			(product) => product.metadata?.category?.toLowerCase() === filters.category?.toLowerCase(),
		);
	}

	// Price range filter
	if (filters.priceRange) {
		filtered = filtered.filter((product) => {
			const price = product.default_price.unit_amount || 0;
			return (
				price >= (filters.priceRange?.min || 0) * 100 && // Convert to cents
				price <= (filters.priceRange?.max || Infinity) * 100
			);
		});
	}

	return filtered;
}

function sortProducts(
	products: Commerce.MappedProduct[],
	sortBy?: SearchFilters["sortBy"],
): Commerce.MappedProduct[] {
	if (!sortBy || sortBy === "relevance") return products;

	return [...products].sort((a, b) => {
		switch (sortBy) {
			case "price-asc":
				return (a.default_price.unit_amount || 0) - (b.default_price.unit_amount || 0);
			case "price-desc":
				return (b.default_price.unit_amount || 0) - (a.default_price.unit_amount || 0);
			case "name":
				return a.name.localeCompare(b.name);
			case "newest":
				return new Date(b.created).getTime() - new Date(a.created).getTime();
			default:
				return 0;
		}
	});
}

function generateSuggestions(query: string, products: Commerce.MappedProduct[]): string[] {
	const suggestions = new Set<string>();
	const queryLower = query.toLowerCase();

	// Add category suggestions
	products.forEach((product) => {
		const category = product.metadata?.category;
		if (category?.toLowerCase().includes(queryLower)) {
			suggestions.add(category);
		}
	});

	// Add product name suggestions
	products.forEach((product) => {
		const words = product.name.toLowerCase().split(" ");
		words.forEach((word) => {
			if (word.includes(queryLower) && word.length > 2) {
				suggestions.add(word);
			}
		});
	});

	return Array.from(suggestions).slice(0, 5);
}

function extractMetadata(products: Commerce.MappedProduct[]) {
	const categories = new Set<string>();
	let minPrice = Infinity;
	let maxPrice = 0;

	products.forEach((product) => {
		if (product.metadata?.category) {
			categories.add(product.metadata.category);
		}
		const price = (product.default_price.unit_amount || 0) / 100; // Convert from cents
		minPrice = Math.min(minPrice, price);
		maxPrice = Math.max(maxPrice, price);
	});

	return {
		categories: Array.from(categories),
		priceRange: {
			min: minPrice === Infinity ? 0 : minPrice,
			max: maxPrice,
		},
	};
}

export const enhancedSearch = unstable_cache(
	async (options: SearchOptions): Promise<SearchResult> => {
		const { query, filters, limit = 20, offset = 0 } = options;

		try {
			const fuse = await getSearchIndex();
			let results: Commerce.MappedProduct[];

			if (query.trim()) {
				// Perform fuzzy search
				const fuseResults = fuse.search(query);
				results = fuseResults.map((result) => result.item);
			} else {
				// No query, return all products
				results = cachedProducts;
			}

			// Apply filters
			const filteredResults = applyFilters(results, filters);

			// Sort results
			const sortedResults = sortProducts(filteredResults, filters?.sortBy);

			// Paginate
			const paginatedResults = sortedResults.slice(offset, offset + limit);

			// Generate suggestions and metadata
			const suggestions = query.trim() ? generateSuggestions(query, cachedProducts) : [];
			const metadata = extractMetadata(cachedProducts);

			return {
				products: paginatedResults,
				totalCount: sortedResults.length,
				suggestions,
				categories: metadata.categories,
				priceRange: metadata.priceRange,
			};
		} catch (error) {
			console.error("Enhanced search failed:", error);
			return {
				products: [],
				totalCount: 0,
				suggestions: [],
				categories: [],
				priceRange: { min: 0, max: 0 },
			};
		}
	},
	["enhanced-search"],
	{
		tags: ["enhanced-search", "products"],
		revalidate: 300, // Cache for 5 minutes
	},
);

// Get popular products based on category
export const getPopularProducts = unstable_cache(
	async (category?: string, limit = 6): Promise<Commerce.MappedProduct[]> => {
		try {
			const products = await safeProductBrowse({ first: 100 });
			let filtered = products;

			if (category) {
				filtered = products.filter(
					(product) => product.metadata?.category?.toLowerCase() === category.toLowerCase(),
				);
			}

			// Sort by creation date (newest first) as a proxy for popularity
			return filtered
				.sort((a, b) => new Date(b.created).getTime() - new Date(a.created).getTime())
				.slice(0, limit);
		} catch (error) {
			console.error("Failed to get popular products:", error);
			return [];
		}
	},
	["popular-products"],
	{
		tags: ["popular-products", "products"],
		revalidate: 600, // Cache for 10 minutes
	},
);

// Get product recommendations based on current product
export const getRecommendations = unstable_cache(
	async (productId: string, limit = 4): Promise<Commerce.MappedProduct[]> => {
		try {
			const products = await safeProductBrowse({ first: 100 });
			const currentProduct = products.find((p) => p.id === productId);

			if (!currentProduct) return [];

			// Find products in the same category
			const sameCategory = products.filter(
				(product) =>
					product.id !== productId && product.metadata?.category === currentProduct.metadata?.category,
			);

			// If not enough in same category, add products from other categories
			if (sameCategory.length < limit) {
				const others = products
					.filter(
						(product) =>
							product.id !== productId && product.metadata?.category !== currentProduct.metadata?.category,
					)
					.slice(0, limit - sameCategory.length);

				return [...sameCategory, ...others].slice(0, limit);
			}

			return sameCategory.slice(0, limit);
		} catch (error) {
			console.error("Failed to get recommendations:", error);
			return [];
		}
	},
	["product-recommendations"],
	{
		tags: ["product-recommendations", "products"],
		revalidate: 600, // Cache for 10 minutes
	},
);
