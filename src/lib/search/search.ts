import * as Commerce from "commerce-kit";
import { unstable_cache } from "next/cache";
import { simpleSearch } from "./simplesearch";

export const searchProducts = unstable_cache(
	async (query: string) => {
		let products: Awaited<ReturnType<typeof Commerce.productBrowse>> = [];

		try {
			products = await Commerce.productBrowse({ first: 100 });
		} catch (error) {
			console.error("Failed to fetch products for search:", error);
			return [];
		}

		const searchResults = simpleSearch(products, query);
		return searchResults.map((sr) => products.find((p) => p.id === sr.id)).filter(Boolean);
	},
	["search", "products"],
	{
		tags: ["search", "products"],
	},
);
