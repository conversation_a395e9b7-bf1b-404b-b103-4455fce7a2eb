"use client";

import type * as Commerce from "commerce-kit";
import Image from "next/image";
import { useEffect, useState } from "react";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { formatMoney } from "@/lib/utils";
import { YnsLink } from "./yns-link";

interface ProductRecommendationsProps {
	productId?: string;
	category?: string;
	title?: string;
	limit?: number;
}

export function ProductRecommendations({
	productId,
	category,
	title = "You might also like",
	limit = 4,
}: ProductRecommendationsProps) {
	const [products, setProducts] = useState<Commerce.MappedProduct[]>([]);
	const [loading, setLoading] = useState(true);

	useEffect(() => {
		const fetchRecommendations = async () => {
			setLoading(true);
			try {
				const params = new URLSearchParams();

				if (productId) params.append("productId", productId);
				if (category) params.append("category", category);
				params.append("limit", limit.toString());

				const response = await fetch(`/api/recommendations?${params.toString()}`);
				if (response.ok) {
					const data = (await response.json()) as { products?: Commerce.MappedProduct[] };
					setProducts(data.products || []);
				}
			} catch (error) {
				console.error("Failed to fetch recommendations:", error);
			} finally {
				setLoading(false);
			}
		};

		fetchRecommendations();
	}, [productId, category, limit]);

	if (loading) {
		return (
			<Card className="w-full">
				<CardHeader>
					<CardTitle>{title}</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-4">
						{Array.from({ length: limit }).map((_, idx) => (
							<div key={idx} className="animate-pulse">
								<div className="aspect-square bg-gray-200 rounded-lg mb-3"></div>
								<div className="h-4 bg-gray-200 rounded mb-2"></div>
								<div className="h-4 bg-gray-200 rounded w-2/3"></div>
							</div>
						))}
					</div>
				</CardContent>
			</Card>
		);
	}

	if (products.length === 0) {
		return null;
	}

	return (
		<Card className="w-full">
			<CardHeader>
				<CardTitle className="text-xl font-semibold">{title}</CardTitle>
			</CardHeader>
			<CardContent>
				<div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-4 gap-6">
					{products.map((product) => (
						<RecommendationCard key={product.id} product={product} />
					))}
				</div>
			</CardContent>
		</Card>
	);
}

interface RecommendationCardProps {
	product: Commerce.MappedProduct;
}

function RecommendationCard({ product }: RecommendationCardProps) {
	const [addingToCart, setAddingToCart] = useState(false);

	const handleAddToCart = async (e: React.MouseEvent) => {
		e.preventDefault();
		setAddingToCart(true);

		try {
			const formData = new FormData();
			formData.append("productId", product.id);

			const response = await fetch("/api/cart/add", {
				method: "POST",
				body: formData,
			});

			if (response.ok) {
				// Show success feedback briefly
				setTimeout(() => setAddingToCart(false), 1000);
			} else {
				setAddingToCart(false);
			}
		} catch (error) {
			console.error("Failed to add to cart:", error);
			setAddingToCart(false);
		}
	};

	if (!product.metadata?.slug) {
		return null;
	}

	return (
		<div className="group relative">
			<YnsLink href={`/product/${product.metadata.slug}`} className="block">
				<div className="aspect-square w-full overflow-hidden rounded-lg bg-gray-100 mb-3">
					{product.images[0] ? (
						<Image
							className="w-full h-full object-cover object-center group-hover:scale-105 transition-transform duration-200"
							src={product.images[0]}
							width={300}
							height={300}
							alt={product.name}
							sizes="(max-width: 640px) 50vw, (max-width: 1024px) 25vw, 20vw"
						/>
					) : (
						<div className="w-full h-full bg-gray-200 flex items-center justify-center">
							<svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
								<path
									strokeLinecap="round"
									strokeLinejoin="round"
									strokeWidth={1}
									d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
								/>
							</svg>
						</div>
					)}
				</div>

				<h3 className="text-sm font-medium text-gray-900 group-hover:text-indigo-600 transition-colors line-clamp-2 mb-1">
					{product.name}
				</h3>

				{product.default_price.unit_amount && (
					<p className="text-sm font-semibold text-gray-900 mb-2">
						{formatMoney({
							amount: product.default_price.unit_amount,
							currency: product.default_price.currency,
							locale: "en-US",
						})}
					</p>
				)}
			</YnsLink>

			<Button
				size="sm"
				onClick={handleAddToCart}
				disabled={addingToCart}
				className="w-full bg-indigo-600 hover:bg-indigo-700 text-white text-xs"
			>
				{addingToCart ? (
					<>
						<svg
							className="animate-spin -ml-1 mr-1 h-3 w-3 text-white"
							xmlns="http://www.w3.org/2000/svg"
							fill="none"
							viewBox="0 0 24 24"
						>
							<circle
								className="opacity-25"
								cx="12"
								cy="12"
								r="10"
								stroke="currentColor"
								strokeWidth="4"
							></circle>
							<path
								className="opacity-75"
								fill="currentColor"
								d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
							></path>
						</svg>
						Adding...
					</>
				) : (
					"Add to Cart"
				)}
			</Button>
		</div>
	);
}
