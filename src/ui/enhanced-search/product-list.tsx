"use client";

import type * as Commerce from "commerce-kit";
import Image from "next/image";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { formatMoney } from "@/lib/utils";
import { YnsLink } from "@/ui/yns-link";

interface ProductListProps {
	products: Commerce.MappedProduct[];
}

export const ProductList = ({ products }: ProductListProps) => {
	const [addingToCart, setAddingToCart] = useState<string | null>(null);

	const validProducts = products.filter(
		(product) => product.metadata?.slug && typeof product.metadata.slug === "string",
	);

	const handleAddToCart = async (productId: string) => {
		setAddingToCart(productId);
		try {
			const formData = new FormData();
			formData.append("productId", productId);

			const response = await fetch("/api/cart/add", {
				method: "POST",
				body: formData,
			});

			if (response.ok) {
				// Show success feedback
				setTimeout(() => setAddingToCart(null), 1000);
			} else {
				setAddingToCart(null);
			}
		} catch (error) {
			console.error("Failed to add to cart:", error);
			setAddingToCart(null);
		}
	};

	if (validProducts.length === 0) {
		// If no products have slugs but products exist, show configuration message
		if (products.length > 0) {
			return (
				<div className="flex flex-col items-center justify-center py-12 text-center">
					<div className="text-amber-400 mb-4">
						<svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path
								strokeLinecap="round"
								strokeLinejoin="round"
								strokeWidth={1}
								d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L4.082 16.5c-.77.833.192 2.5 1.732 2.5z"
							/>
						</svg>
					</div>
					<h3 className="text-lg font-medium text-gray-700 mb-2">Products need configuration</h3>
					<p className="text-gray-500 mb-2">
						Found {products.length} product{products.length !== 1 ? "s" : ""}, but they need slug metadata in Stripe.
					</p>
					<p className="text-sm text-gray-400">
						Add &quot;slug&quot; metadata to your Stripe products to display them here.
					</p>
				</div>
			);
		}

		// No products found at all
		return (
			<div className="flex flex-col items-center justify-center py-12 text-center">
				<div className="text-gray-400 mb-4">
					<svg className="w-16 h-16 mx-auto" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path
							strokeLinecap="round"
							strokeLinejoin="round"
							strokeWidth={1}
							d="M20 13V6a2 2 0 00-2-2H6a2 2 0 00-2 2v7m16 0v5a2 2 0 01-2 2H6a2 2 0 01-2-2v-5m16 0h-2M4 13h2m13-8V4a1 1 0 00-1-1H7a1 1 0 00-1 1v1m8 0V4.5"
						/>
					</svg>
				</div>
				<h3 className="text-lg font-medium text-gray-700 mb-2">No products found</h3>
				<p className="text-gray-500">Try adjusting your search terms or filters</p>
			</div>
		);
	}

	return (
		<div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4">
			{validProducts.map((product, idx) => {
				const isAddingToCart = addingToCart === product.id;

				return (
					<div
						key={product.id}
						className="group relative bg-white rounded-lg border border-gray-200 hover:shadow-lg transition-shadow duration-200"
					>
						<YnsLink href={`/product/${product.metadata.slug}`} className="block">
							<div className="aspect-square w-full overflow-hidden rounded-t-lg bg-gray-100">
								{product.images[0] ? (
									<Image
										className="w-full h-full object-cover object-center group-hover:scale-105 transition-transform duration-200"
										src={product.images[0]}
										width={400}
										height={400}
										loading={idx < 8 ? "eager" : "lazy"}
										priority={idx < 4}
										sizes="(max-width: 640px) 100vw, (max-width: 1024px) 50vw, (max-width: 1280px) 33vw, 25vw"
										alt={product.name}
									/>
								) : (
									<div className="w-full h-full bg-gray-200 flex items-center justify-center">
										<svg
											className="w-12 h-12 text-gray-400"
											fill="none"
											stroke="currentColor"
											viewBox="0 0 24 24"
										>
											<path
												strokeLinecap="round"
												strokeLinejoin="round"
												strokeWidth={1}
												d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
											/>
										</svg>
									</div>
								)}
							</div>
						</YnsLink>

						<div className="p-4">
							<YnsLink href={`/product/${product.metadata.slug}`}>
								<h3 className="text-lg font-semibold text-gray-900 hover:text-indigo-600 transition-colors line-clamp-2">
									{product.name}
								</h3>
							</YnsLink>

							{product.description && (
								<p className="mt-2 text-sm text-gray-600 line-clamp-2">{product.description}</p>
							)}

							<div className="mt-3 flex items-center justify-between">
								<div>
									{product.default_price.unit_amount && (
										<p className="text-lg font-bold text-gray-900">
											{formatMoney({
												amount: product.default_price.unit_amount,
												currency: product.default_price.currency,
												locale: "en-US",
											})}
										</p>
									)}
									{product.metadata?.category && (
										<p className="text-xs text-gray-500 capitalize">{product.metadata.category}</p>
									)}
								</div>

								<Button
									size="sm"
									onClick={(e) => {
										e.preventDefault();
										handleAddToCart(product.id);
									}}
									disabled={isAddingToCart}
									className="bg-indigo-600 hover:bg-indigo-700 text-white"
								>
									{isAddingToCart ? (
										<>
											<svg
												className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
												xmlns="http://www.w3.org/2000/svg"
												fill="none"
												viewBox="0 0 24 24"
											>
												<circle
													className="opacity-25"
													cx="12"
													cy="12"
													r="10"
													stroke="currentColor"
													strokeWidth="4"
												></circle>
												<path
													className="opacity-75"
													fill="currentColor"
													d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
												></path>
											</svg>
											Adding...
										</>
									) : (
										"Add to Cart"
									)}
								</Button>
							</div>
						</div>

						{/* Quick view overlay on hover */}
						<div className="absolute inset-0 bg-black bg-opacity-0 group-hover:bg-opacity-10 transition-all duration-200 rounded-lg pointer-events-none" />
					</div>
				);
			})}
		</div>
	);
};
